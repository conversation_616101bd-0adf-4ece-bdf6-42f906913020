require('dotenv').config();
const axios = require('axios');
const { faker } = require('@faker-js/faker');

class SimpleChatbotTest {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || 'http://localhost:3000';
    this.authURL = 'http://localhost:3001';
    this.chatbotURL = 'http://localhost:3006';
    this.token = null;
    this.userId = null;
    this.conversationId = null;
    
    // Test data
    this.testUser = {
      email: `testuser_${Date.now()}@example.com`,
      username: `testuser_${Math.random().toString(36).substring(2, 8)}`,
      password: 'TestPassword123!',
      full_name: faker.person.fullName()
    };
  }

  async run() {
    console.log('🤖 Starting Simple Chatbot Test');
    console.log('================================');
    
    try {
      await this.setup();
      await this.testChatbotService();
      await this.testConversationFlow();
      await this.testMessageExchange();
      await this.testErrorHandling();
      
      console.log('\n✅ All tests passed successfully!');
      return { success: true };
      
    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      return { success: false, error: error.message };
    } finally {
      await this.cleanup();
    }
  }

  async setup() {
    console.log('\n🔧 Setting up test environment...');
    
    // Register user
    try {
      const registerResponse = await axios.post(`${this.authURL}/auth/register`, this.testUser);
      console.log('✅ User registered successfully');
      this.token = registerResponse.data.data.token;
      this.userId = registerResponse.data.data.user.id;
    } catch (error) {
      throw new Error(`Registration failed: ${error.response?.data?.message || error.message}`);
    }

    // Login user
    try {
      const loginResponse = await axios.post(`${this.authURL}/auth/login`, {
        email: this.testUser.email,
        password: this.testUser.password
      });
      console.log('✅ User logged in successfully');
      this.token = loginResponse.data.data.token;
    } catch (error) {
      throw new Error(`Login failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async testChatbotService() {
    console.log('\n🔍 Testing Chatbot Service Health...');
    
    try {
      // Test direct chatbot service health
      const healthResponse = await axios.get(`${this.chatbotURL}/health`);
      console.log('✅ Chatbot service is healthy:', healthResponse.data);
      
      // Test through API Gateway
      const gatewayHealthResponse = await axios.get(`${this.baseURL}/api/chatbot/health`);
      console.log('✅ Chatbot service accessible through API Gateway');
      
    } catch (error) {
      throw new Error(`Chatbot service health check failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async testConversationFlow() {
    console.log('\n💬 Testing Conversation Creation...');
    
    try {
      // Create a basic conversation
      const conversationData = {
        title: `Test Conversation - ${Date.now()}`,
        context_type: 'general',
        context_data: {},
        metadata: {
          test_session: true,
          created_by: 'simple_test'
        }
      };

      const createResponse = await axios.post(
        `${this.baseURL}/api/chatbot/conversations`,
        conversationData,
        {
          headers: { Authorization: `Bearer ${this.token}` }
        }
      );

      console.log('📋 Create response:', createResponse.data);

      if (createResponse.data.success && createResponse.data.data.conversation?.id) {
        this.conversationId = createResponse.data.data.conversation.id;
        console.log('✅ Conversation created successfully:', this.conversationId);
      } else {
        console.error('❌ Conversation creation response:', createResponse.data);
        throw new Error('Conversation creation failed');
      }

      // Get conversation details
      const getResponse = await axios.get(
        `${this.baseURL}/api/chatbot/conversations/${this.conversationId}`,
        {
          headers: { Authorization: `Bearer ${this.token}` }
        }
      );

      if (getResponse.data.success) {
        console.log('✅ Conversation retrieved successfully');
      } else {
        throw new Error('Conversation retrieval failed');
      }

    } catch (error) {
      console.error('❌ Conversation creation error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
      throw new Error(`Conversation flow test failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async testMessageExchange() {
    console.log('\n📝 Testing Message Exchange...');
    
    if (!this.conversationId) {
      throw new Error('No conversation ID available for message testing');
    }

    const testMessages = [
      "Hello, I'm testing the chatbot service.",
      "Can you help me with career guidance?"
    ];

    try {
      for (let i = 0; i < testMessages.length; i++) {
        const message = testMessages[i];
        console.log(`\n📤 Sending message ${i + 1}: "${message.substring(0, 50)}..."`);

        const messageData = {
          content: message,
          content_type: 'text'
        };

        const startTime = Date.now();
        let response;
        try {
          response = await axios.post(
            `${this.baseURL}/api/chatbot/conversations/${this.conversationId}/messages`,
            messageData,
            {
              headers: { Authorization: `Bearer ${this.token}` }
            }
          );
        } catch (error) {
          console.error(`❌ Message ${i + 1} failed:`, {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message
          });
          throw error;
        }
        const responseTime = Date.now() - startTime;

        if (response.data.success && response.data.data.user_message && response.data.data.assistant_message) {
          console.log(`✅ Message ${i + 1} exchange successful:`);
          console.log(`   - Response time: ${responseTime}ms`);
          console.log(`   - User message ID: ${response.data.data.user_message.id}`);
          console.log(`   - Assistant message ID: ${response.data.data.assistant_message.id}`);
          console.log(`   - Assistant response: "${response.data.data.assistant_message.content.substring(0, 100)}..."`);
          
          if (response.data.data.usage) {
            console.log(`   - Tokens used: ${response.data.data.usage.total_tokens || 'unknown'}`);
          }
        } else {
          throw new Error(`Message ${i + 1} exchange failed`);
        }

        // Small delay between messages
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Test message retrieval
      try {
        const messagesResponse = await axios.get(
          `${this.baseURL}/api/chatbot/conversations/${this.conversationId}/messages?include_usage=true`,
          {
            headers: { Authorization: `Bearer ${this.token}` }
          }
        );

        console.log('📋 Messages response:', messagesResponse.data);

        if (messagesResponse.data.messages && messagesResponse.data.messages.length > 0) {
          console.log(`✅ Message retrieval successful: ${messagesResponse.data.messages.length} messages found`);
        } else {
          console.log('⚠️ Message retrieval returned empty or unexpected format, but continuing test');
        }
      } catch (error) {
        console.log('⚠️ Message retrieval failed (non-critical):', error.response?.data || error.message);
      }

    } catch (error) {
      throw new Error(`Message exchange test failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async testErrorHandling() {
    console.log('\n🚨 Testing Error Handling...');
    
    try {
      // Test invalid conversation ID
      try {
        await axios.post(
          `${this.baseURL}/api/chatbot/conversations/invalid-id/messages`,
          { content: 'Test message', content_type: 'text' },
          { headers: { Authorization: `Bearer ${this.token}` } }
        );
        throw new Error('Invalid conversation ID should have failed');
      } catch (error) {
        if (error.response?.status === 404) {
          console.log('✅ Invalid conversation ID properly rejected');
        } else {
          throw error;
        }
      }

      // Test empty message
      try {
        await axios.post(
          `${this.baseURL}/api/chatbot/conversations/${this.conversationId}/messages`,
          { content: '', content_type: 'text' },
          { headers: { Authorization: `Bearer ${this.token}` } }
        );
        throw new Error('Empty message should have failed');
      } catch (error) {
        if (error.response?.status === 400) {
          console.log('✅ Empty message properly rejected');
        } else {
          throw error;
        }
      }

      // Test unauthorized access
      try {
        await axios.get(`${this.baseURL}/api/chatbot/conversations`);
        throw new Error('Unauthorized access should have failed');
      } catch (error) {
        if (error.response?.status === 401) {
          console.log('✅ Unauthorized access properly rejected');
        } else {
          throw error;
        }
      }

    } catch (error) {
      throw new Error(`Error handling test failed: ${error.message}`);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    try {
      // Delete conversation if created
      if (this.conversationId && this.token) {
        try {
          await axios.delete(
            `${this.baseURL}/api/chatbot/conversations/${this.conversationId}`,
            { headers: { Authorization: `Bearer ${this.token}` } }
          );
          console.log('✅ Test conversation deleted');
        } catch (error) {
          console.log('⚠️ Failed to delete conversation (non-critical)');
        }
      }

      // Logout user
      if (this.token) {
        try {
          await axios.post(
            `${this.authURL}/auth/logout`,
            {},
            { headers: { Authorization: `Bearer ${this.token}` } }
          );
          console.log('✅ User logged out');
        } catch (error) {
          console.log('⚠️ Failed to logout (non-critical)');
        }
      }

    } catch (error) {
      console.log('⚠️ Cleanup error (non-critical):', error.message);
    }
  }
}

// Run test if called directly
if (require.main === module) {
  const test = new SimpleChatbotTest();
  test.run()
    .then((result) => {
      console.log('\n📊 Test Result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('\n💥 Test crashed:', error);
      process.exit(1);
    });
}

module.exports = SimpleChatbotTest;
